import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, ScrollView, ActivityIndicator } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { getDonors, getDivisions, getZilas, getUpazilas } from '../api';

const BLOOD_GROUPS = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

export default function SearchDonorsScreen() {
  const [bloodGroup, setBloodGroup] = useState('');
  const [divisionId, setDivisionId] = useState('');
  const [zilaId, setZilaId] = useState('');
  const [upazilaId, setUpazilaId] = useState('');
  const [divisions, setDivisions] = useState([]);
  const [zilas, setZilas] = useState([]);
  const [upazilas, setUpazilas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [donors, setDonors] = useState([]);
  const [error, setError] = useState('');

  useEffect(() => {
    getDivisions().then(setDivisions).catch(() => setError('Failed to load divisions'));
  }, []);

  useEffect(() => {
    if (divisionId) {
      getZilas(divisionId).then(setZilas).catch(() => setError('Failed to load zilas'));
    } else {
      setZilas([]);
      setZilaId('');
    }
  }, [divisionId]);

  useEffect(() => {
    if (zilaId) {
      getUpazilas(zilaId).then(setUpazilas).catch(() => setError('Failed to load upazilas'));
    } else {
      setUpazilas([]);
      setUpazilaId('');
    }
  }, [zilaId]);

  const search = async () => {
    setLoading(true);
    setError('');
    try {
      const params = {};
      if (bloodGroup) params.bloodGroup = bloodGroup;
      if (divisionId) params.divisionId = divisionId;
      if (zilaId) params.zilaId = zilaId;
      if (upazilaId) params.upazilaId = upazilaId;
      const data = await getDonors(params);
      setDonors(data);
    } catch (e) {
      setError('Failed to fetch donors');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={{ paddingBottom: 32 }}>
      <Text style={styles.title}>Search Donors</Text>
      <Text style={styles.label}>Blood Group</Text>
      <Picker
        selectedValue={bloodGroup}
        onValueChange={setBloodGroup}
        style={styles.picker}
      >
        <Picker.Item label="Select Blood Group" value="" />
        {BLOOD_GROUPS.map(bg => <Picker.Item key={bg} label={bg} value={bg} />)}
      </Picker>
      <Text style={styles.label}>Division</Text>
      <Picker
        selectedValue={divisionId}
        onValueChange={setDivisionId}
        style={styles.picker}
      >
        <Picker.Item label="Select Division" value="" />
        {divisions.map((d) => <Picker.Item key={d.id} label={d.name} value={d.id} />)}
      </Picker>
      <Text style={styles.label}>Zila</Text>
      <Picker
        selectedValue={zilaId}
        onValueChange={setZilaId}
        style={styles.picker}
        enabled={!!divisionId}
      >
        <Picker.Item label="Select Zila" value="" />
        {zilas.map((z) => <Picker.Item key={z.id} label={z.name} value={z.id} />)}
      </Picker>
      <Text style={styles.label}>Upazila</Text>
      <Picker
        selectedValue={upazilaId}
        onValueChange={setUpazilaId}
        style={styles.picker}
        enabled={!!zilaId}
      >
        <Picker.Item label="Select Upazila" value="" />
        {upazilas.map((u) => <Picker.Item key={u.id} label={u.name} value={u.id} />)}
      </Picker>
      <TouchableOpacity style={styles.button} onPress={search} disabled={loading}>
        <Text style={styles.buttonText}>{loading ? 'Searching...' : 'Search'}</Text>
      </TouchableOpacity>
      {error ? <Text style={styles.error}>{error}</Text> : null}
      {loading && <ActivityIndicator size="32" color="#d90429" style={{ marginTop: 20 }} />}
      <FlatList
        data={donors}
        keyExtractor={item => item.id.toString()}
        renderItem={({ item }) => (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>{item.firstName} {item.lastName}</Text>
            <Text style={styles.cardSubtitle}>Blood Group: {item.bloodGroup}</Text>
            <Text style={styles.cardSubtitle}>Phone: {item.phoneNumber}</Text>
            <Text style={styles.cardSubtitle}>Location: {item.currentLocation}</Text>
            <Text style={styles.cardSubtitle}>Last Donation: {item.lastDonationDate}</Text>
          </View>
        )}
        ListEmptyComponent={!loading && <Text style={styles.empty}>No donors found.</Text>}
        style={{ marginTop: 24 }}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#d90429',
    marginBottom: 16,
    textAlign: 'center',
  },
  label: {
    fontSize: 16,
    marginTop: 12,
    marginBottom: 4,
    color: '#333',
  },
  picker: {
    backgroundColor: '#f2f2f2',
    borderRadius: 8,
    marginBottom: 8,
  },
  button: {
    backgroundColor: '#d90429',
    paddingVertical: 14,
    borderRadius: 30,
    marginTop: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  error: {
    color: '#d90429',
    marginTop: 10,
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#d90429',
  },
  cardSubtitle: {
    fontSize: 15,
    color: '#333',
    marginTop: 2,
  },
  empty: {
    textAlign: 'center',
    color: '#888',
    marginTop: 24,
  },
}); 